import numpy as np
import matplotlib.pyplot as plt
from tensorboard.backend.event_processing.event_accumulator import EventAccumulator

import numpy as np

event_smoe = EventAccumulator('/cm/archive/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_standard_lb/tensorboard')

# Point to the directory (or file) containing your event files.
event_softmax = EventAccumulator('/cm/archive/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_standard_lb/tensorboard')
event_softmax.Reload()  # Load the events
balancing_loss_softmax = event_softmax.Scalars('train/loss')

# Point to the directory (or file) containing your event files.
event_sigmoid = EventAccumulator('/cm/archive/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_deepseek_sigmoidonly/tensorboard')
event_sigmoid.Reload()  # Load the events
event_shared = EventAccumulator('/home/<USER>/moeut_training_code/tensorboard/158m/shared/tensorboard')
event_shared.Reload()  # Load the events
event_deepseek = EventAccumulator('/home/<USER>/moeut_training_code/tensorboard/158m/deepseek/tensorboard')
event_deepseek.Reload()  # Load the events


steps = list(set([event_smoe.Scalars('train/loss')[i].step for i in range(len(event_smoe.Scalars('train/loss')))]))
steps.sort()
steps_validation = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
log_info = {
    "training_loss": {
        "smoe": filter_duplicate_steps(event_smoe.Scalars('train/loss')),
        "sigmoid": filter_duplicate_steps(event_sigmoid.Scalars('train/loss')),
        "shared": filter_duplicate_steps(event_shared.Scalars('train/loss')),
        "deepseek": filter_duplicate_steps(event_deepseek.Scalars('train/loss'))
    },
    "balancing_loss": {
        "smoe": filter_duplicate_steps(event_smoe.Scalars('train/reg_loss/moe')),
        "sigmoid": filter_duplicate_steps(event_sigmoid.Scalars('train/reg_loss/moe')),
        "shared": filter_duplicate_steps(event_shared.Scalars('train/reg_loss/moe')),
        "deepseek": filter_duplicate_steps(event_deepseek.Scalars('train/reg_loss/moe'))
    },
    "validation_loss": {
        "smoe": filter_duplicate_steps(event_smoe.Scalars('validation/val/loss')),
        "sigmoid": filter_duplicate_steps(event_sigmoid.Scalars('validation/val/loss')),
        "shared": filter_duplicate_steps(event_shared.Scalars('validation/val/loss')),
        "deepseek": filter_duplicate_steps(event_deepseek.Scalars('validation/val/loss'))
    }
}

balancing_loss_softmax[0].step

balancing_loss_sigmoid[0].step

steps = list(set([event.step for event in balancing_loss_softmax]))

values_softmax = {}
values_sigmoid = {}
for event in balancing_loss_softmax:
    if event.step in values_softmax.keys(): continue
    values_softmax[event.step] = event.value
for event in balancing_loss_sigmoid:
    if event.step in values_sigmoid.keys(): continue
    values_sigmoid[event.step] = event.value

# Only get intersection of steps
steps = list(set(values_softmax.keys()).intersection(set(values_sigmoid.keys())))
steps.sort()

len(values_softmax.values()), len(values_sigmoid.keys()), len(steps)

plt.figure(figsize=(16, 10))

# Convert dictionary values to lists for smoothing
x_values = steps
# only get value in steps
y_softmax = [values_softmax[step] for step in steps]
y_sigmoid = [values_sigmoid[step] for step in steps]

# Apply Savitzky-Golay filter for smoothing
# Window length must be odd and less than data length
window = 51  # Adjust this value to control smoothing amount
y_softmax_smooth = savgol_filter(y_softmax, window, 3)
y_sigmoid_smooth = savgol_filter(y_sigmoid, window, 3)

# Plot both original (faint) and smoothed lines
plt.plot(x_values, y_softmax, alpha=0.1, color='blue', label='Softmax (raw)')
plt.plot(x_values, y_sigmoid, alpha=0.1, color='orange', label='DeepSeek (raw)')
plt.plot(x_values, y_softmax_smooth, color='blue', label='Softmax (smoothed)', linewidth=2)
plt.plot(x_values, y_sigmoid_smooth, color='orange', label='DeepSeek (smoothed)', linewidth=2)

plt.title("Language Loss")
plt.grid(True, alpha=0.3)
plt.legend()
plt.show()

y_softmax

steps = list(range(10000, 100001, 10000))

balancing_loss_softmax_val = event_softmax.Scalars('validation/val/loss')
balancing_loss_sigmoid_val = event_sigmoid.Scalars('validation/val/loss')
print(steps)

y_softmax = [balancing_loss_softmax_val[int(step/10000) - 1].value for step in steps]
y_sigmoid = [balancing_loss_sigmoid_val[int(step/10000) - 1].value for step in steps]

plt.plot(steps, y_softmax)
plt.plot(steps, y_sigmoid)


plt.plot

from scipy.signal import savgol_filter

plt.figure(figsize=(16, 10))

# Convert dictionary values to lists for smoothing
x_values = list(values_softmax.keys())
y_softmax = list(values_softmax.values())
y_sigmoid = list(values_sigmoid.values())

# Apply Savitzky-Golay filter for smoothing
# Window length must be odd and less than data length
window = 101  # Adjust this value to control smoothing amount
y_softmax_smooth = savgol_filter(y_softmax, window, 3)
y_sigmoid_smooth = savgol_filter(y_sigmoid, window, 3)

# Plot both original (faint) and smoothed lines
# plt.plot(x_values, y_softmax, alpha=0.2, color='blue', label='Softmax (raw)')
# plt.plot(x_values, y_sigmoid, alpha=0.2, color='orange', label='Sigmoid (raw)')
plt.plot(x_values, y_softmax_smooth, color='blue', label='Softmax (smoothed)', linewidth=2)
plt.plot(x_values, y_sigmoid_smooth, color='orange', label='Sigmoid (smoothed)', linewidth=2)

plt.title("Load Balancing Loss")
plt.grid(True, alpha=0.3)
plt.legend()
plt.show()

plt.figure(figsize=(16, 10))

plt.plot(values_softmax.keys(), values_softmax.values(), label='Softmax')
plt.plot(values_softmax.keys(), values_sigmoid.values(), label='Sigmoid')

plt.title("Load Balancing Loss")
plt.grid()
plt.legend()  # Add this line to show the legend
plt.show()

import numpy as np

np.load("/home/<USER>/moeut_training_code/paper/deepseek/router_saturation_679M/smoe/model-60000.npy")

import torch
checkpoint = torch.load("/cm/shared/thongdt4/moeut_training_code/save/test/checkpoint/model-200.pth")

num_params = 0
num_active_params = 0

for key in list(checkpoint['model'].keys()):
    num_params += checkpoint['model'][key].numel()

    if "pkm.keys" in key or "pkm.values" in key:
        num_active_params += int(checkpoint['model'][key].numel() / 8)
    else:
        num_active_params += checkpoint['model'][key].numel()

for key in list(checkpoint['model'].keys())[:20]:
    print(f"{key:50}, {checkpoint['model'][key].numel()}")

print("\nNumber of parameters: ", num_params)
print("\nNumber of activated parameters: ", num_active_params)

checkpoint_200 = checkpoint['model']["unique_layers.0.pkm.film_ensemble"]

checkpoint_100 = checkpoint['model']["unique_layers.0.pkm.film_ensemble"]

print(checkpoint_100[:100])
print(checkpoint_200[:100])

import pandas as pd
import json
import numpy as np
import os
import glob

result_dir = "/cm/archive/thongdt4/moeut_training_code/save_final/slimpajama_moe_no_attmoe_154M_standard_lb/export"
# print(os.path.join(result_dir + "/export", "result-model-200000*.json"))
all_json_files = glob.glob(result_dir + "/*.json")
all_json_files = sorted(all_json_files, key=lambda x: int(x.split('-')[-1].split('.')[0]))
# all_json_files = [all_json_files[-1]]
all_json_files

fields = [
    "val/perplexity",
    "lambada/accuracy/total",
    "blimp/accuracy/group_average",
    "cbt/accuracy/seq_average",
    "hellaswag/accuracy/seq_average",
    "piqa/accuracy/seq_average",
    # "ai2arc/accuracy/ARC-Easy",
    "ai2arc/accuracy/ARC-Challenge",
    "race/accuracy/group_average",
    "siqa/accuracy/seq_average",
    "commonsenseqa/accuracy/seq_average"
]

result_list = []
for file_path in all_json_files:
    file_name = os.path.basename(file_path)
    # print(file_path)
    with open(file_path) as f:
        result_data = json.load(f)

    result_table_item = {}
    result_table_item["model_name"] = file_name
    for field in fields:
        try:
            result_table_item[field] = result_data[field]
        except:
            result_table_item[field] = None

    result_list.append(result_table_item)

result_table = pd.DataFrame.from_dict(result_list, orient='columns')
result_table



import json

with open("/cm/archive/thongdt4/moeut_training_code/cache/BoolQ/data/dev.jsonl", "r") as f:
    data = [json.loads(line) for line in f]

data[0]['answer']

import numpy as np
import pandas as pd
import os
import glob

logit = np.load("/cm/archive/thongdt4/moeut_training_code/paper/deepseek/logits/158M/smoe/model-10000.npy")

logit[0][0]

import numpy as np
import matplotlib.pyplot as plt

numerical_exp_data = np.load("/cm/archive/thongdt4/moeut_training_code/numerical_experiments/export/results.npy")

def plot_voronoi_style_error(sample_sizes, errors, std_errors=None, filename='voronoi_style_error.pdf'):
    """
    Plot the Voronoi-style error as a function of sample size.

    Parameters
    ----------
    sample_sizes : list or np.ndarray
        List of sample sizes.
    errors : list or np.ndarray
        List of Voronoi-style errors corresponding to each sample size.
    std_errors : list or np.ndarray, optional
        Standard errors for each point, used for error bars.
    filename : str
        Name of the file to save the plot to.
    title : str
        Title of the plot.
    """
    # Ensure the export directory exists
    export_dir = '/cm/archive/thongdt4/moeut_training_code/paper/deepseek/numerical_exp'
    os.makedirs(export_dir, exist_ok=True)

    # Convert to numpy arrays
    sample_sizes = np.array(sample_sizes)
    errors = np.array(errors)
    if std_errors is not None:
        std_errors = np.array(std_errors)

    # Fit a power law curve: y = a * x^b
    from scipy.optimize import curve_fit

    def power_law(x, a, b):
        return a * x**b

    # Fit the curve
    params, _ = curve_fit(power_law, sample_sizes, errors)
    a, b = params

    # Generate points for the fitted curve
    x_fit = np.logspace(np.log10(min(sample_sizes)), np.log10(max(sample_sizes)), 100)
    y_fit = power_law(x_fit, a, b)

    # Create the plot
    plt.figure(figsize=(8, 5))

    # Plot the fitted curve
    plt.plot(x_fit, y_fit, '--', color='orange', label=f'{a:.2f}n^{b:.5f}')

    # Plot the data points with error bars if provided
    if std_errors is not None:
        plt.errorbar(sample_sizes, errors, yerr=std_errors, fmt='-o', color='blue', label='D₁(Ĝₙ, G*)')
    else:
        plt.plot(sample_sizes, errors, '-o', color='blue', label='D₁(Ĝₙ, G*)')

    # Use log scales for both axes to match the example
    plt.xscale('log')
    plt.yscale('log')
    plt.xlabel('log(sample size)')
    plt.ylabel('log(loss)')
    # plt.grid(True, which='both', linestyle='--', alpha=0.2)
    plt.legend()
    plt.tight_layout()
    plt.savefig(os.path.join(export_dir, filename))
    plt.show()
    plt.close()


plot_voronoi_style_error(numerical_exp_data[0], numerical_exp_data[1], numerical_exp_data[2], filename='voronoi_style_error_sigmoid.pdf')



import json

def merge_json(file1_path, file2_path):
    with open(file1_path) as f:
        result_data1 = json.load(f)
    with open(file2_path) as f:
        result_data2 = json.load(f)

    # merge data of result_data2 to result_data1 (except for the fields that are already in result_data1), but still update 'cbt' fields
    for key, value in result_data2.items():
        if key in result_data1.keys():
            if "cbt" in key:
                result_data1[key] = value
            else:
                continue
        else:
            result_data1[key] = value

    # save the merged data to a new file
    with open(file1_path, "w") as f:
        json.dump(result_data1, f)

    print(f"Merge {file1_path} successfully!")

# test the function
# file1_path = "/home/<USER>/moeut_training_code/paper/deepseek/result/result-model-10000.pth-1.json"
# file2_path = "/home/<USER>/moeut_training_code/paper/deepseek/result/result-model-10000.pth-2.json"
# merge_json(file1_path, file2_path)

foler_result_1 = "/cm/shared/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_standard_lb/export"
foler_result_2 = "/cm/shared/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_standard_lb/tmp"

# loop through all files in folder_result_2 and merge them to the corresponding files in folder_result_1
for file_name in os.listdir(foler_result_2):
    file1_path = os.path.join(foler_result_1, file_name)
    file2_path = os.path.join(foler_result_2, file_name)
    merge_json(file1_path, file2_path)

import json

with open("/cm/shared/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_sigmoid_standard_lb/tmp/result-model-90000.pth.json") as f:
    data = json.load(f)

data["cbt/accuracy/seq_average"]

data['Data'][0]

import torch

checkpoint = torch.load("/cm/shared/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_deepseek/checkpoint/model-100000.pth")

for key in list(checkpoint['model'].keys())[:16]:
    print(f"{key:50}, {checkpoint['model'][key].numel()}")

checkpoint['model']['unique_layers.1.pkm.e_score_correction_bias']

import pandas as pd

import pandas as pd

# Read the Parquet file
df = pd.read_parquet("/cm/shared/thongdt4/moeut_training_code/cache/SQUADCompletionTest/validation-00000-of-00001.parquet")

# Write to JSON Lines format
df.to_json("/cm/shared/thongdt4/moeut_training_code/cache/SQUADCompletionTest/data/squad_validation.jsonl", orient='records', lines=True)

import json

def _convert_to_jsonl(json_file: str, jsonl_file: str):
    """Convert SQuAD JSON to JSONL format"""
    try:
        with open(json_file, 'r') as f:
            squad_data = json.load(f)

        examples = []
        for article in squad_data['data']:
            for paragraph in article['paragraphs']:
                context = paragraph['context']
                for qa in paragraph['qas']:
                    examples.append({
                        'id': qa['id'],
                        'context': context,
                        'question': qa['question'],
                        'answers': qa['answers']
                    })

        with open(jsonl_file, 'w') as f:
            for example in examples:
                f.write(json.dumps(example) + '\n')

    except Exception as e:
        print(f"Error converting SQuAD format: {e}")

_convert_to_jsonl(
    "/home/<USER>/moeut_training_code/cache/SQUAD/data/dev-v1.1.json",
    "/home/<USER>/moeut_training_code/cache/SQUAD/data/dev-v1.1.jsonl"
)

import pandas as pd

df = pd.read_csv("/cm/shared/thongdt4/moeut_training_code/cache/MMLU/test/abstract_algebra_test.csv")
df.head(2)

list(df.iloc[0])

import json

with open("/cm/shared/thongdt4/moeut_training_code/cache/RACE/RACE/test/high/321.txt", "r", encoding="utf-8") as f:
    data = json.load(f)
data

import numpy as np
import json
import pandas as pd

df_arc = pd.read_csv("/home/<USER>/moeut_training_code/cache/AI2ARC/ARC-V1-Feb2018-2/ARC-Challenge/ARC-Challenge-Test.csv")
df_arc = df_arc[df_arc["AnswerKey"].isin(["A", "B", "C", "D"])]
df_arc.head(5)

count_anwer_key = df_arc["AnswerKey"].value_counts()
count_anwer_key

int(df_arc['AnswerKey'][0] - 'A')

import numpy as np

# load /home/<USER>/moeut_training_code/cache/Winogrande/winogrande_1.1/dev-labels.lst

with open("/home/<USER>/moeut_training_code/cache/Winogrande/winogrande_1.1/dev-labels.lst", "r") as f:
    labels = f.read().splitlines()

# statistics of labels
np.unique(labels, return_counts=True)