{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "\n", "# Ensure no W&B logging will be performed\n", "sys.argv = \"main.py -log tb -name tst -reset 1 -lm.eval.enable 0 -log tb -batch_size 20 -restore /cm/shared/thongdt4/moeut_training_code/tmp/smoe-softmax-10000.pth\".split(\" \")\n", "# sys.argv = \"main.py -log tb -name tst -reset 1 -lm.eval.enable 0 -log tb -batch_size 20 -restore paper/moe_universal/checkpoints/plvywltl/model.ckpt\".split(\" \")\n", "\n", "# Pretend we are in the main directory\n", "os.chdir(\"../../\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/cm/archive/thongdt4/miniconda3/envs/moeut/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-04-02 16:35:10,519] [INFO] [real_accelerator.py:222:get_accelerator] Setting ds_accelerator to cuda (auto detect)\n", "Warning: The cache directory for DeepSpeed Triton autotune, /home/<USER>/.triton/autotune, appears to be on an NFS system. While this is generally acceptable, if you experience slowdowns or hanging when DeepSpeed exits, it is recommended to set the TRITON_CACHE_DIR environment variable to a non-NFS path.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/cm/archive/thongdt4/miniconda3/envs/moeut/bin/../lib/gcc/x86_64-conda-linux-gnu/11.2.0/../../../../x86_64-conda-linux-gnu/bin/ld: cannot find -laio: No such file or directory\n", "collect2: error: ld returned 1 exit status\n", "/cm/archive/thongdt4/miniconda3/envs/moeut/bin/../lib/gcc/x86_64-conda-linux-gnu/11.2.0/../../../../x86_64-conda-linux-gnu/bin/ld: cannot find -laio: No such file or directory\n", "collect2: error: ld returned 1 exit status\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<class 'layers.moe_layer.MoE'>\n"]}], "source": ["from main import initialize\n", "import torch\n", "import torch.nn.functional as F\n", "from layers import MoE\n", "\n", "print(MoE)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["%matplotlib inline\n", "from matplotlib import pyplot as plt\n", "from matplotlib.ticker import ScalarFormatter\n", "\n", "plt.rcParams['text.usetex'] = False #Let TeX do the typsetting\n", "plt.rcParams['text.latex.preamble'] = '\\\\usepackage{sansmath}\\n\\\\sansmath' #Force sans-serif math mode (for axes labels)\n", "plt.rcParams['font.family'] = 'DejaVu Sans' # Change from 'Arial' to 'sans-serif'\n", "plt.rcParams['font.sans-serif'] = 'Arial' # Choose a nice font here\n", "\n", "plt.rcParams['figure.dpi'] = 200\n", "plt.rcParams['savefig.dpi'] = 200"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Restoring: /cm/shared/thongdt4/moeut_training_code/tmp/smoe-softmax-10000.pth...\n", "No distributed environment detected\n", "Resetting training state...\n", "Port already used: 7000\n", "Port already used: 7001\n", "Found tensorboard in /cm/archive/thongdt4/miniconda3/envs/moeut/bin/tensorboard\n", "Starting Tensorboard server on 7002\n", "Done.\n", "====================================================================================================\n", "Available GPUs: 8\n", "====================================================================================================\n", "SlimPajama: Generating map...\n", "Map done.\n", "SlimPajama: Loaded tokenizer.\n", "SlimPajama: 58345 chunks missing\n", "Token limit reached. No need to tokenize more.\n", "SlimPajama: Limiting to first 164 chunks because limited to 2050048000 tokens\n", "SlimPajama: Loaded tokenizer.\n", "SlimPajama: 31345 chunks missing\n", "Token limit reached. No need to tokenize more.\n", "SlimPajama: Limiting to first 59 chunks because limited to 1032192 tokens\n", "Weight info:\n", "  n_model_weights: 149733376\n", "  n_attention_weights: 10747904\n", "  n_non_attnetion_weights: 138985472\n", "  attention_precent: 0.0717802823065981\n", "Training in bfloat16...\n", "Total number of model parameters: 157934400\n", "Loading state\n", "Loading run_invariants\n", "Loading sampler\n", "Loading interface\n", "Loading optimizer\n", "Loading scaler\n", "Loading model\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/cm/shared/thongdt4/moeut_training_code/tasks/lm_base.py:115: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at ../torch/csrc/utils/tensor_new.cpp:275.)\n", "  data = torch.tensor(res, dtype=torch.long, device=self.helper.device).T\n"]}], "source": ["helper, task = initialize()\n", "task.create_data_fetcher()\n", "\n", "orig_run_model_valid = task.run_model_validation"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["nexp = task.helper.args.moe.n_experts\n", "ntok = task.helper.args.sentencepiece.n_pieces\n", "# ngrp = task.helper.args.transformer.universal.group_size\n", "nlayers = task.helper.args.transformer.encoder_n_layers\n", "context = task.helper.args.lm.unroll\n", "k = task.helper.args.pkm.n_heads\n", "bsz = task.helper.args.batch_size\n", "ngrp = 16\n", "\n", "token_counts = 0\n", "\n", "cnt = 0\n", "simmap = torch.zeros(ngrp, nlayers // ngrp, nlayers // ngrp)\n", "\n", "thissel = torch.zeros(ngrp, nlayers // ngrp, bsz, context, k, dtype=torch.long)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["16"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["ngrp"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["global this_data\n", "\n", "def run_model_validation(self, data):\n", "    global token_counts\n", "    global this_data\n", "    global cnt\n", "    global simmap\n", "\n", "    token_counts = token_counts + F.one_hot(data[\"data\"].flatten().long(), ntok).sum(0)\n", "\n", "    this_data = data\n", "\n", "    thissel.zero_()\n", "\n", "    res = orig_run_model_valid(data)\n", "\n", "    ohsel = F.one_hot(thissel, nexp).sum(-2)\n", "    ohsel = (ohsel.flatten(2,3).permute(0, 2, 1, 3) > 0).float()\n", "\n", "    #shape: ngrp, bsz*context, nlayer, nexp\n", "    overlap = torch.einsum(\"nglk,ngok->nglo\", ohsel, ohsel)\n", "    norm = torch.maximum(ohsel.unsqueeze(-3), ohsel.unsqueeze(-2)).sum(-1)\n", "    simcnt = (overlap / norm).sum(1)\n", "    cnt += ohsel.shape[1]\n", "    simmap += simcnt\n", "\n", "    return res\n", "\n", "task.run_model_validation = run_model_validation.__get__(task)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["<tasks.slimpajama_transformer.SlimpajamaTransformer at 0x15535d23ffa0>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["task"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["id_map = {}\n", "\n", "def patch_module(module):\n", "\n", "    myid = id(module)\n", "    if myid in id_map:\n", "        return\n", "\n", "    gid = len(id_map)\n", "    id_map[myid] = gid\n", "\n", "    # sel_val, sel_index = self.topk(\n", "\n", "    def new_topk(self, *args, **kwargs):\n", "        gid = id_map[id(self)]\n", "\n", "        sel_val, sel_index = MoE.topk(self, *args, **kwargs)\n", "\n", "        thissel[gid, self.layer//ngrp] = sel_index\n", "\n", "        return sel_val, sel_index\n", "\n", "\n", "    module.topk = new_topk.__get__(module)\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["for m in task.model.modules():\n", "    if isinstance(m, MoE):\n", "        patch_module(m)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting validation on val...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/100 [00:00<?, ?it/s]\n"]}, {"ename": "RuntimeError", "evalue": "The expanded size of the tensor (20) must match the existing size (10) at non-singleton dimension 0.  Target sizes: [20, 1024, 8].  Tensor sizes: [10, 1024, 8]", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[11], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43mtask\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mvalidate\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/cm/shared/thongdt4/moeut_training_code/tasks/lm_eval_mixin.py:169\u001b[0m, in \u001b[0;36mLMEvalMixin.validate\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    166\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mvalidate\u001b[39m(\u001b[38;5;28mself\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m <PERSON><PERSON>[Any, \u001b[38;5;28mfloat\u001b[39m]:\n\u001b[1;32m    167\u001b[0m     is_end \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhelper\u001b[38;5;241m.\u001b[39mstate\u001b[38;5;241m.\u001b[39miter \u001b[38;5;241m==\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhelper\u001b[38;5;241m.\u001b[39margs\u001b[38;5;241m.\u001b[39mstop_after\n\u001b[0;32m--> 169\u001b[0m     res \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mvalidate\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    170\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m name \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mprob_compare_valid_sets:\n\u001b[1;32m    171\u001b[0m         \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mStarting validation on \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mname\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m...\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m/cm/shared/thongdt4/moeut_training_code/framework/task/task.py:326\u001b[0m, in \u001b[0;36mTask.validate\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    325\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mvalidate\u001b[39m(\u001b[38;5;28mself\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Dict[\u001b[38;5;28mstr\u001b[39m, Any]:\n\u001b[0;32m--> 326\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mvalidate_on_names\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mvalid_sets\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mkeys\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/cm/shared/thongdt4/moeut_training_code/framework/task/task.py:305\u001b[0m, in \u001b[0;36mTask.validate_on_names\u001b[0;34m(self, name_it)\u001b[0m\n\u001b[1;32m    302\u001b[0m logging_layer\u001b[38;5;241m.\u001b[39mget_logs(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmodel)\n\u001b[1;32m    304\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m name \u001b[38;5;129;01min\u001b[39;00m name_it:\n\u001b[0;32m--> 305\u001b[0m     test, loss \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mvalidate_on_name\u001b[49m\u001b[43m(\u001b[49m\u001b[43mname\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    307\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhelper\u001b[38;5;241m.\u001b[39margs\u001b[38;5;241m.\u001b[39mdump_logs:\n\u001b[1;32m    308\u001b[0m         logging_layer\u001b[38;5;241m.\u001b[39mdump_logs(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmodel, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhelper\u001b[38;5;241m.\u001b[39mget_storage_path(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlog_dumps\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;241m+\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m/\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhelper\u001b[38;5;241m.\u001b[39mstate\u001b[38;5;241m.\u001b[39miter\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m/valid/\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mname\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m/\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m/cm/shared/thongdt4/moeut_training_code/tasks/lm_eval_mixin.py:137\u001b[0m, in \u001b[0;36mLMEvalMixin.validate_on_name\u001b[0;34m(self, name)\u001b[0m\n\u001b[1;32m    135\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstate_enabled \u001b[38;5;241m=\u001b[39m name \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlambada\u001b[39m\u001b[38;5;124m\"\u001b[39m}\n\u001b[1;32m    136\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpad_quantum \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m128\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m name \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlambada\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m--> 137\u001b[0m res \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mvalidate_on\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mvalid_sets\u001b[49m\u001b[43m[\u001b[49m\u001b[43mname\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mvalid_loaders\u001b[49m\u001b[43m[\u001b[49m\u001b[43mname\u001b[49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    138\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpad_quantum \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    139\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstate_enabled \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n", "File \u001b[0;32m/cm/shared/thongdt4/moeut_training_code/tasks/lm_base.py:47\u001b[0m, in \u001b[0;36mLMBase.validate_on\u001b[0;34m(self, set, loader)\u001b[0m\n\u001b[1;32m     45\u001b[0m state \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmodel_interface\u001b[38;5;241m.\u001b[39mstate\n\u001b[1;32m     46\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmodel_interface\u001b[38;5;241m.\u001b[39mreset_state()\n\u001b[0;32m---> 47\u001b[0m res \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mvalidate_on\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mset\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mloader\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     48\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmodel_interface\u001b[38;5;241m.\u001b[39mstate \u001b[38;5;241m=\u001b[39m state\n\u001b[1;32m     49\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m res\n", "File \u001b[0;32m/cm/shared/thongdt4/moeut_training_code/framework/task/task.py:236\u001b[0m, in \u001b[0;36mTask.validate_on\u001b[0;34m(self, set, loader)\u001b[0m\n\u001b[1;32m    234\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m d \u001b[38;5;129;01min\u001b[39;00m tqdm(loader):\n\u001b[1;32m    235\u001b[0m     d \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mprepare_data(d)\n\u001b[0;32m--> 236\u001b[0m     loss, output \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun_model_validation\u001b[49m\u001b[43m(\u001b[49m\u001b[43md\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    237\u001b[0m     batch_size \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mget_batch_size(d)\n\u001b[1;32m    239\u001b[0m     this_loss \u001b[38;5;241m=\u001b[39m loss\u001b[38;5;241m.\u001b[39msum()\u001b[38;5;241m.\u001b[39mitem() \u001b[38;5;241m*\u001b[39m batch_size\n", "Cell \u001b[0;32mIn[7], line 15\u001b[0m, in \u001b[0;36mrun_model_validation\u001b[0;34m(self, data)\u001b[0m\n\u001b[1;32m     11\u001b[0m this_data \u001b[38;5;241m=\u001b[39m data\n\u001b[1;32m     13\u001b[0m thissel\u001b[38;5;241m.\u001b[39mzero_()\n\u001b[0;32m---> 15\u001b[0m res \u001b[38;5;241m=\u001b[39m \u001b[43morig_run_model_valid\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     17\u001b[0m ohsel \u001b[38;5;241m=\u001b[39m F\u001b[38;5;241m.\u001b[39mone_hot(thissel, nexp)\u001b[38;5;241m.\u001b[39msum(\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m2\u001b[39m)\n\u001b[1;32m     18\u001b[0m ohsel \u001b[38;5;241m=\u001b[39m (ohsel\u001b[38;5;241m.\u001b[39mflatten(\u001b[38;5;241m2\u001b[39m,\u001b[38;5;241m3\u001b[39m)\u001b[38;5;241m.\u001b[39mpermute(\u001b[38;5;241m0\u001b[39m, \u001b[38;5;241m2\u001b[39m, \u001b[38;5;241m1\u001b[39m, \u001b[38;5;241m3\u001b[39m) \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m0\u001b[39m)\u001b[38;5;241m.\u001b[39mfloat()\n", "File \u001b[0;32m/cm/shared/thongdt4/moeut_training_code/framework/task/task.py:201\u001b[0m, in \u001b[0;36mTask.run_model_validation\u001b[0;34m(self, data)\u001b[0m\n\u001b[1;32m    200\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mrun_model_validation\u001b[39m(\u001b[38;5;28mself\u001b[39m, data: Dict[\u001b[38;5;28mstr\u001b[39m, torch\u001b[38;5;241m.\u001b[39mTensor]) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m <PERSON><PERSON>[torch\u001b[38;5;241m.\u001b[39mTensor, Any]:\n\u001b[0;32m--> 201\u001b[0m     res, _ \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun_model\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    202\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m res\u001b[38;5;241m.\u001b[39mloss, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmodel_interface\u001b[38;5;241m.\u001b[39mdecode_outputs(res)\n", "File \u001b[0;32m/cm/shared/thongdt4/moeut_training_code/tasks/lm_eval_mixin.py:90\u001b[0m, in \u001b[0;36mLMEvalMixin.run_model\u001b[0;34m(self, data, *args, **kwargs)\u001b[0m\n\u001b[1;32m     87\u001b[0m         data \u001b[38;5;241m=\u001b[39m {k: v \u001b[38;5;28;01mfor\u001b[39;00m k, v \u001b[38;5;129;01min\u001b[39;00m data\u001b[38;5;241m.\u001b[39mitems()}\n\u001b[1;32m     88\u001b[0m         data[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdata\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m F\u001b[38;5;241m.\u001b[39mpad(data[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdata\u001b[39m\u001b[38;5;124m\"\u001b[39m], [\u001b[38;5;241m0\u001b[39m] \u001b[38;5;241m*\u001b[39m ((data[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdata\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mndim\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m)\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m2\u001b[39m) \u001b[38;5;241m+\u001b[39m [\u001b[38;5;241m0\u001b[39m, tlen\u001b[38;5;241m-\u001b[39molen], value\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0\u001b[39m, mode\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mconstant\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m---> 90\u001b[0m res, d  \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun_model\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     92\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpad_quantum \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m tlen \u001b[38;5;241m!=\u001b[39m olen:\n\u001b[1;32m     93\u001b[0m     res\u001b[38;5;241m.\u001b[39moutputs \u001b[38;5;241m=\u001b[39m res\u001b[38;5;241m.\u001b[39moutputs[:olen\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m]\n", "File \u001b[0;32m/cm/shared/thongdt4/moeut_training_code/tasks/transformer_lm_mixin.py:364\u001b[0m, in \u001b[0;36mTransformerLMMixin.run_model\u001b[0;34m(self, data, ubatch)\u001b[0m\n\u001b[1;32m    361\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39minput_history\u001b[38;5;241m.\u001b[39mpop(\u001b[38;5;241m0\u001b[39m)\n\u001b[1;32m    362\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39minput_history\u001b[38;5;241m.\u001b[39mappend(data[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdata\u001b[39m\u001b[38;5;124m\"\u001b[39m][:, \u001b[38;5;241m0\u001b[39m])\n\u001b[0;32m--> 364\u001b[0m res, plots \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun_model\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mubatch\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    366\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m plot_now \u001b[38;5;129;01mor\u001b[39;00m is_dumping:\n\u001b[1;32m    367\u001b[0m     plots\u001b[38;5;241m.\u001b[39mupdate({\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mactivations/\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mk\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m: v \u001b[38;5;28;01mfor\u001b[39;00m k, v \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mvisualizer\u001b[38;5;241m.\u001b[39mplot()\u001b[38;5;241m.\u001b[39mitems()})\n", "File \u001b[0;32m/cm/shared/thongdt4/moeut_training_code/framework/task/task.py:198\u001b[0m, in \u001b[0;36mTask.run_model\u001b[0;34m(self, data, ubatch)\u001b[0m\n\u001b[1;32m    197\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mrun_model\u001b[39m(\u001b[38;5;28mself\u001b[39m, data: Dict[\u001b[38;5;28mstr\u001b[39m, torch\u001b[38;5;241m.\u001b[39mTensor], ubatch: \u001b[38;5;28mint\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m <PERSON><PERSON>[Result, Dict[\u001b[38;5;28mstr\u001b[39m, Any]]:\n\u001b[0;32m--> 198\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmodel_interface\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mhelper\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstate\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43miter\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mubatch\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/cm/shared/thongdt4/moeut_training_code/interfaces/language_model_interface.py:87\u001b[0m, in \u001b[0;36mLanguageModelInterface.__call__\u001b[0;34m(self, data, iter, ubatch)\u001b[0m\n\u001b[1;32m     84\u001b[0m mask \u001b[38;5;241m=\u001b[39m data[\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmask_name]\u001b[38;5;241m.\u001b[39mnarrow(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtime_dim, \u001b[38;5;241m1\u001b[39m, data[\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmask_name]\u001b[38;5;241m.\u001b[39mshape[\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtime_dim] \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m1\u001b[39m)\u001b[38;5;241m.\u001b[39mcontiguous() \u001b[38;5;28;01mif\u001b[39;00m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmask_name \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmask_name \u001b[38;5;129;01min\u001b[39;00m data) \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m     86\u001b[0m plots \u001b[38;5;241m=\u001b[39m {}\n\u001b[0;32m---> 87\u001b[0m res, state \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmodel\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtarget\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstate\u001b[49m\u001b[43m[\u001b[49m\u001b[43mubatch\u001b[49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     88\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(res, torch\u001b[38;5;241m.\u001b[39mnn\u001b[38;5;241m.\u001b[39mmodules\u001b[38;5;241m.\u001b[39madaptive\u001b[38;5;241m.\u001b[39m_ASMoutput):\n\u001b[1;32m     89\u001b[0m     loss \u001b[38;5;241m=\u001b[39m res\u001b[38;5;241m.\u001b[39mloss\n", "File \u001b[0;32m/cm/archive/thongdt4/miniconda3/envs/moeut/lib/python3.10/site-packages/torch/nn/modules/module.py:1511\u001b[0m, in \u001b[0;36mModule._wrapped_call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1509\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_compiled_call_impl(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[1;32m   1510\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1511\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/cm/archive/thongdt4/miniconda3/envs/moeut/lib/python3.10/site-packages/torch/nn/modules/module.py:1520\u001b[0m, in \u001b[0;36mModule._call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1515\u001b[0m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[1;32m   1516\u001b[0m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[1;32m   1517\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_pre_hooks\n\u001b[1;32m   1518\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[1;32m   1519\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[0;32m-> 1520\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1522\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m   1523\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m/cm/shared/thongdt4/moeut_training_code/models/transformer_language_model.py:150\u001b[0m, in \u001b[0;36mTransformerLanguageModel.forward\u001b[0;34m(self, x, target, state)\u001b[0m\n\u001b[1;32m    147\u001b[0m     cross_layer_state, net_o \u001b[38;5;241m=\u001b[39m l(cross_layer_state, inp, mask\u001b[38;5;241m=\u001b[39mmask, attend_to\u001b[38;5;241m=\u001b[39mattend_to,\n\u001b[1;32m    148\u001b[0m                                  pos_offset\u001b[38;5;241m=\u001b[39mpos_offset)\n\u001b[1;32m    149\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 150\u001b[0m     net_o \u001b[38;5;241m=\u001b[39m \u001b[43ml\u001b[49m\u001b[43m(\u001b[49m\u001b[43minp\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmask\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmask\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mattend_to\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mattend_to\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpos_offset\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mpos_offset\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    152\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m plot_cossim:\n\u001b[1;32m    153\u001b[0m     features\u001b[38;5;241m.\u001b[39mappend(net_o)\n", "File \u001b[0;32m/cm/archive/thongdt4/miniconda3/envs/moeut/lib/python3.10/site-packages/torch/nn/modules/module.py:1511\u001b[0m, in \u001b[0;36mModule._wrapped_call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1509\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_compiled_call_impl(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[1;32m   1510\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1511\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/cm/archive/thongdt4/miniconda3/envs/moeut/lib/python3.10/site-packages/torch/nn/modules/module.py:1520\u001b[0m, in \u001b[0;36mModule._call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1515\u001b[0m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[1;32m   1516\u001b[0m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[1;32m   1517\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_pre_hooks\n\u001b[1;32m   1518\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[1;32m   1519\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[0;32m-> 1520\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1522\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m   1523\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m/cm/shared/thongdt4/moeut_training_code/layers/transformer/relative_moe_transformer.py:161\u001b[0m, in \u001b[0;36mRelativeMoeTransformerEncoderLayer.forward\u001b[0;34m(self, src, mask, attend_to, pos_offset)\u001b[0m\n\u001b[1;32m    158\u001b[0m     src2 \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mnorm1(mlp_input)\n\u001b[1;32m    159\u001b[0m     src \u001b[38;5;241m=\u001b[39m src2\n\u001b[0;32m--> 161\u001b[0m src3 \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpkm\u001b[49m\u001b[43m(\u001b[49m\u001b[43msrc2\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    163\u001b[0m src \u001b[38;5;241m=\u001b[39m src \u001b[38;5;241m+\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdropout(src3)\n\u001b[1;32m    165\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpreln:\n", "File \u001b[0;32m/cm/archive/thongdt4/miniconda3/envs/moeut/lib/python3.10/site-packages/torch/nn/modules/module.py:1511\u001b[0m, in \u001b[0;36mModule._wrapped_call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1509\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_compiled_call_impl(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[1;32m   1510\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1511\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/cm/archive/thongdt4/miniconda3/envs/moeut/lib/python3.10/site-packages/torch/nn/modules/module.py:1520\u001b[0m, in \u001b[0;36mModule._call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1515\u001b[0m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[1;32m   1516\u001b[0m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[1;32m   1517\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_pre_hooks\n\u001b[1;32m   1518\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[1;32m   1519\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[0;32m-> 1520\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1522\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m   1523\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m/cm/shared/thongdt4/moeut_training_code/layers/moe_layer.py:351\u001b[0m, in \u001b[0;36mMoE.forward\u001b[0;34m(self, input)\u001b[0m\n\u001b[1;32m    348\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    349\u001b[0m     sel2 \u001b[38;5;241m=\u001b[39m sel\n\u001b[0;32m--> 351\u001b[0m sel_val, sel_index \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtopk\u001b[49m\u001b[43m(\u001b[49m\u001b[43msel2\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mn_heads\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    352\u001b[0m \u001b[38;5;66;03m### norm gate to sum 1\u001b[39;00m\n\u001b[1;32m    353\u001b[0m denominator \u001b[38;5;241m=\u001b[39m sel_val\u001b[38;5;241m.\u001b[39msum(dim\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m, keepdim\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m) \u001b[38;5;241m+\u001b[39m \u001b[38;5;241m1e-20\u001b[39m\n", "Cell \u001b[0;32mIn[9], line 19\u001b[0m, in \u001b[0;36mpatch_module.<locals>.new_topk\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m     15\u001b[0m gid \u001b[38;5;241m=\u001b[39m id_map[\u001b[38;5;28mid\u001b[39m(\u001b[38;5;28mself\u001b[39m)]\n\u001b[1;32m     17\u001b[0m sel_val, sel_index \u001b[38;5;241m=\u001b[39m MoE\u001b[38;5;241m.\u001b[39mtopk(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[0;32m---> 19\u001b[0m \u001b[43mthissel\u001b[49m\u001b[43m[\u001b[49m\u001b[43mgid\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mlayer\u001b[49m\u001b[38;5;241;43m/\u001b[39;49m\u001b[38;5;241;43m/\u001b[39;49m\u001b[43mngrp\u001b[49m\u001b[43m]\u001b[49m \u001b[38;5;241m=\u001b[39m sel_index\n\u001b[1;32m     21\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m sel_val, sel_index\n", "\u001b[0;31mRuntimeError\u001b[0m: The expanded size of the tensor (20) must match the existing size (10) at non-singleton dimension 0.  Target sizes: [20, 1024, 8].  Tensor sizes: [10, 1024, 8]"]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m<PERSON><PERSON> crashed while executing code in the current cell or a previous cell. \n", "\u001b[1;31m<PERSON><PERSON>se review the code in the cell(s) to identify a possible cause of the failure. \n", "\u001b[1;31mClick <a href='https://aka.ms/vscodeJupyterKernelCrash'>here</a> for more info. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["task.validate()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["avg = simmap/cnt"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gid = 0\n", "fig, ax=plt.subplots(figsize=(2.4,2))\n", "plt.imshow(avg[0], cmap='viridis', aspect='auto')\n", "plt.colorbar()\n", "plt.xticks([a*2 + gid for a in range(nlayers // ngrp // 2 + 1)], [str(a*4 + 1 + gid) for a in range(nlayers // ngrp // 2 + 1)])\n", "plt.yticks([a*2 + gid for a in range(nlayers // ngrp // 2 + 1)], [str(a*4 + 1 + gid) for a in range(nlayers // ngrp // 2+1)])\n", "plt.xlabel(\"Layer\")\n", "plt.ylabel(\"Layer\")\n", "plt.tight_layout()\n", "plt.savefig(\"paper/moe_universal/layer_similarity.pdf\", bbox_inches='tight')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}