import torch
import torch.nn as nn

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import os
import sys
import glob

import pandas as pd
def top_k_numpy(logits: np.ndarray, top_k: int, dim: int) -> np.ndarray:
    """Implementation of top_k in pytorch using n<PERSON><PERSON>'s partition function.
    Args:
        logits:  shape (L, T, N) — L layers, T tokens, N experts
        top_k:   K, number of experts selected per token
        dim:     dimension to perform top_k on
    Returns:
        top_k_index:  shape (L, T, N) - values at top_k positions different from 0, other is 0
        top_k_logits: shape (L, T, N) - values at top_k positions are the original values, other is 0
    """
    top_k_index = np.zeros_like(logits)
    top_k_index[np.arange(logits.shape[0])[:, None, None], np.arange(logits.shape[1])[None, :, None], np.argpartition(logits, -top_k, axis=dim)[:, :, -top_k:]] = 1

    top_k_value = logits * top_k_index

    return top_k_index, top_k_value

def match_stats(router_choose, router_final_choose):
    n_layer = router_choose.shape[0]
    count_match = {}
    for layer in range(n_layer):
        count_match[layer] = 0
        router_choose_layer = router_choose[layer]
        router_final_choose_layer = router_final_choose[layer]

        # compare the choose of router_10k and router_final based only on the 1 value
        for i in range(router_choose_layer.shape[0]):
            for j in range(router_choose_layer[i].shape[0]):
                if router_choose_layer[i][j] == 1 and router_final_choose_layer[i][j] == 1:
                    count_match[layer] += 1
    return count_match
def calculate_expert_changes(epoch_1, epoch_2):
    changes = 0
    for e1, e2 in zip(epoch_1, epoch_2):
        if e1 != e2:
            changes += 1
    return changes / len(epoch_1)

def router_change_rate(logits_folder: str, top_k: int = 8, n_tokens: int = 6000) -> pd.DataFrame:
    router_info_list_path = glob.glob(os.path.join(logits_folder, "*.npy"))
    router_info_list_path.sort(key=lambda x: int(x.split("-")[-1].split(".")[0]))
    breakpoint()

    model_router_change_rate = {}

    for i in range(1, len(router_info_list_path)):
        router_info_curr = torch.tensor(np.load(router_info_list_path[i])[:, :6000, :])
        router_info_prev = torch.tensor(np.load(router_info_list_path[i-1])[:, :6000, :])
        router_info_curr_values, router_info_curr_indices = torch.topk(router_info_curr, k=top_k, dim=-1)
        router_info_prev_values, router_info_prev_indices = torch.topk(router_info_prev, k=top_k, dim=-1)
        router_info_curr_indices = router_info_curr_indices.tolist()
        router_info_prev_indices = router_info_prev_indices.tolist()
        ecr_list = []
        # breakpoint()
        for sentence1, sentence2 in zip(router_info_curr_indices[-2:], router_info_prev_indices[-2:]):
            for token1, token2 in zip(sentence1, sentence2):
                ecr_list.append(calculate_expert_changes(token1, token2))
        # router_info_curr, _ = top_k_numpy(np.load(router_info_list_path[i])[:, :6000, :], top_k=top_k, dim=-1)
        # router_info_prev, _ = top_k_numpy(np.load(router_info_list_path[i-1])[:, :6000, :], top_k=top_k, dim=-1)

        # router_choose_curr, router_choose_prev = (router_info_curr > 0).astype(int), (router_info_prev > 0).astype(int)
        # count_change = match_stats(router_choose_curr, router_choose_prev)
        # count_change_ratio = {layer: 100 - count_change[layer] / router_choose_prev[layer].sum() * 100 for layer in count_change}

        model_router_change_rate[os.path.basename(router_info_list_path[i]).split("-")[-1].split(".")[0]] = np.average(ecr_list)

    # info_df = pd.DataFrame(model_router_change_rate)

    return model_router_change_rate
smoe_158m = router_change_rate("/cm/archive/thongdt4/moeut_training_code/paper/deepseek/logits/158M/smoe2", top_k=8)
xmoe_158m = router_change_rate("/cm/archive/thongdt4/moeut_training_code/paper/deepseek/logits/158M/xmoe", top_k=8)
smoe_sigmoid_158m = router_change_rate("/cm/archive/thongdt4/moeut_training_code/paper/deepseek/logits/158M/smoe_sigmoid", top_k=8)
moe_plus_plus_158m = router_change_rate("/cm/archive/thongdt4/moeut_training_code/paper/deepseek/logits/158M/moe_plus_plus", top_k=8)
smoe_shared_158m = router_change_rate("/cm/archive/thongdt4/moeut_training_code/paper/deepseek/logits/158M/smoe_shared", top_k=6)
smoe_deepseek_158m = router_change_rate("/cm/archive/thongdt4/moeut_training_code/paper/deepseek/logits/158M/smoe_deepseek", top_k=6)
smoe_tcmoe_158m = router_change_rate("/cm/archive/thongdt4/moeut_training_code/paper/deepseek/logits/158M/tcmoe", top_k=8)

router_change_rate_158m_top1 = {
    "smoe": smoe_158m,
    "xmoe": xmoe_158m,
    "smoe_sigmoid": smoe_sigmoid_158m,
    "moe_plus_plus": moe_plus_plus_158m,
    "smoe_shared": smoe_shared_158m,
    "smoe_deepseek": smoe_deepseek_158m,
    "tcmoe": smoe_tcmoe_158m,
}
breakpoint()