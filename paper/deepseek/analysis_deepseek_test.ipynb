{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["'moe_layer'"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "import sys\n", "os.environ['CUDA_VISIBLE_DEVICES'] = '3'\n", "\n", "# Ensure no W&B logging will be performed\n", "sys.argv = \"main.py -log tb -name tst -reset 1 -lm.eval.enable 0 -log tb -batch_size 20 -restore /cm/shared/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_standard_lb/checkpoint/model-100000.pth\".split(\" \")\n", "\n", "# Pretend we are in the main directory\n", "os.chdir(\"../../\")\n", "import os\n", "os.environ[\"MOE_TYPE\"] = \"moe_layer\"\n", "os.environ.get(\"MOE_TYPE\", \"moe_layer\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/cm/archive/thongdt4/miniconda3/envs/moeut/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-04-04 10:29:47,360] [INFO] [real_accelerator.py:222:get_accelerator] Setting ds_accelerator to cuda (auto detect)\n", "Warning: The cache directory for DeepSpeed Triton autotune, /home/<USER>/.triton/autotune, appears to be on an NFS system. While this is generally acceptable, if you experience slowdowns or hanging when DeepSpeed exits, it is recommended to set the TRITON_CACHE_DIR environment variable to a non-NFS path.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/cm/archive/thongdt4/miniconda3/envs/moeut/bin/../lib/gcc/x86_64-conda-linux-gnu/11.2.0/../../../../x86_64-conda-linux-gnu/bin/ld: cannot find -laio: No such file or directory\n", "collect2: error: ld returned 1 exit status\n", "/cm/archive/thongdt4/miniconda3/envs/moeut/bin/../lib/gcc/x86_64-conda-linux-gnu/11.2.0/../../../../x86_64-conda-linux-gnu/bin/ld: cannot find -laio: No such file or directory\n", "collect2: error: ld returned 1 exit status\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<class 'layers.moe_layer.MoE'>\n"]}], "source": ["from main import initialize\n", "import torch\n", "import torch.nn.functional as F\n", "from layers import MoE\n", "import matplotlib.pyplot as plt\n", "import pickle\n", "import numpy as np\n", "\n", "%matplotlib inline\n", "\n", "plt.rcParams['text.usetex'] = False #Let TeX do the typsetting\n", "plt.rcParams['text.latex.preamble'] = '\\\\usepackage{sansmath}\\n\\\\sansmath' #Force sans-serif math mode (for axes labels)\n", "plt.rcParams['font.family'] = 'DejaVu Sans' # Change from 'Arial' to 'sans-serif'\n", "plt.rcParams['font.sans-serif'] = 'Arial' # Choose a nice font here\n", "\n", "plt.rcParams['figure.dpi'] = 200\n", "plt.rcParams['savefig.dpi'] = 200\n", "\n", "print(MoE)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Restoring: /cm/shared/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_standard_lb/checkpoint/model-30000.pth...\n", "No distributed environment detected\n", "Resetting training state...\n", "Port already used: 7000\n", "Port already used: 7001\n", "Port already used: 7002\n", "Found tensorboard in /cm/archive/thongdt4/miniconda3/envs/moeut/bin/tensorboard\n", "Starting Tensorboard server on 7003\n", "ERROR: failed to start Tensorboard server. Server not responding.\n", "WARNING: Some requested GPUs are not available. Using available ones only.\n", "====================================================================================================\n", "Available GPUs: 1\n", "====================================================================================================\n", "SlimPajama: Generating map...\n", "Map done.\n", "SlimPajama: Loaded tokenizer.\n", "SlimPajama: 58345 chunks missing\n", "Token limit reached. No need to tokenize more.\n", "SlimPajama: Limiting to first 164 chunks because limited to 2050048000 tokens\n", "SlimPajama: Loaded tokenizer.\n", "SlimPajama: 31345 chunks missing\n", "Token limit reached. No need to tokenize more.\n", "SlimPajama: Limiting to first 59 chunks because limited to 1032192 tokens\n", "Weight info:\n", "  n_model_weights: 149733376\n", "  n_attention_weights: 10747904\n", "  n_non_attnetion_weights: 138985472\n", "  attention_precent: 0.0717802823065981\n", "Training in bfloat16...\n", "Total number of model parameters: 157934400\n", "Loading state\n", "Loading run_invariants\n", "Loading sampler\n", "Loading interface\n", "Loading optimizer\n", "Loading scaler\n", "Loading model\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/cm/shared/thongdt4/moeut_training_code/tasks/lm_base.py:115: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at ../torch/csrc/utils/tensor_new.cpp:275.)\n", "  data = torch.tensor(res, dtype=torch.long, device=self.helper.device).T\n"]}], "source": ["helper, task = initialize()\n", "task.create_data_fetcher()\n", "\n", "orig_run_model_valid = task.run_model_validation"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["nexp = task.helper.args.moe.n_experts\n", "ntok = task.helper.args.sentencepiece.n_pieces\n", "nlayers = task.helper.args.transformer.encoder_n_layers\n", "ngrp = 16\n", "\n", "token_counts = 0\n", "\n", "counts = torch.zeros(ngrp, nlayers // ngrp, nexp, ntok)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([16, 1, 66, 8000])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["counts.size()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["<tasks.slimpajama_transformer.SlimpajamaTransformer at 0x15535d26bf40>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["global this_data\n", "\n", "def run_model_validation(self, data):\n", "    global token_counts\n", "    global this_data\n", "\n", "    token_counts = token_counts + F.one_hot(data[\"data\"].flatten().long(), ntok).sum(0)\n", "\n", "    this_data = data\n", "    return orig_run_model_valid(data)\n", "\n", "task.run_model_validation = run_model_validation.__get__(task)\n", "task"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["id_map = {}\n", "\n", "def patch_module(module):\n", "\n", "    myid = id(module)\n", "    if myid in id_map:\n", "        return\n", "\n", "    gid = len(id_map)\n", "    id_map[myid] = gid\n", "\n", "    # sel_val, sel_index = self.topk(\n", "\n", "    def new_topk(self, *args, **kwargs):\n", "        nonlocal gid\n", "        global this_data\n", "        data = this_data[\"data\"][:-1].T\n", "\n", "        sel_val, sel_index = MoE.topk(self, *args, **kwargs)\n", "\n", "        assert data.shape == sel_index.shape[:-1]\n", "\n", "        data = data.reshape(-1)\n", "\n", "        # Shape of counts[gid]: nexp, ntok\n", "        # Linear index: expert * ntok + tok\n", "\n", "        seli = sel_index.flatten(end_dim=-2) * ntok\n", "        addi = seli + data[..., None]\n", "        addi = addi.flatten().cpu()\n", "\n", "        counts[gid][self.layer // ngrp].flatten().index_add_(0, addi, torch.ones_like(addi, dtype=torch.float32))\n", "\n", "        return sel_val, sel_index\n", "\n", "\n", "    module.topk = new_topk.__get__(module)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["for m in task.model.modules():\n", "    if isinstance(m, MoE):\n", "        patch_module(m)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting validation on val...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["New shape: (torch.<PERSON><PERSON>([10, 1024, 512]), torch.<PERSON><PERSON>([66, 512, 128]))\n", "New shape: (torch.<PERSON><PERSON>([10, 1024, 8, 128]), torch.<PERSON><PERSON>([66, 128, 512]))\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 100/100 [03:30<00:00,  2.10s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Validation done on worker 0.\n", "Validation accuracy on val: 0.4498486328125\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"data": {"text/plain": ["{'val/loss': 2.812581647872925,\n", " 'val/accuracy': 0.4498486328125,\n", " 'val/perplexity': 16.652854578259007,\n", " 'val/time_since_best_loss': 0,\n", " 'val/time_since_best_accuracy': 0,\n", " 'mean_accuracy': 0.4498486328125,\n", " 'mean_loss': 2.8125816559791565}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["task.validate()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["order = torch.argsort(token_counts, descending=True).cpu()\n", "token_counts_o = token_counts.cpu()[order]\n", "counts_o = counts[:, :, :, order]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["<PERSON>.<PERSON><PERSON>([66])"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["counts_o[0][:,:,0].squeeze().size()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Token 0: ID=7857, Text='.'\n", "Token 1: ID=7859, Text=','\n", "Token 2: ID=10, Text='the'\n", "Token 3: ID=35, Text='of'\n", "Token 4: ID=33, Text='and'\n", "Token 5: ID=31, Text='to'\n", "Token 6: ID=7836, Text=''\n", "Token 7: ID=4, Text='a'\n", "Token 8: ID=32, Text='in'\n", "Token 9: ID=7844, Text='s'\n", "Token 10: ID=7869, Text='1'\n", "Token 11: ID=7868, Text='''\n", "Token 12: ID=7866, Text='0'\n", "Token 13: ID=7870, Text='-'\n", "Token 14: ID=7874, Text='2'\n", "Token 15: ID=64, Text='is'\n", "Token 16: ID=96, Text='that'\n", "Token 17: ID=66, Text='for'\n", "Token 18: ID=29, Text='ed'\n", "Token 19: ID=70, Text='on'\n"]}], "source": ["# Add this code to decode tokens\n", "def decode_tokens(token_ids):\n", "    \"\"\"Decode token IDs to their string representations\"\"\"\n", "    # Use the vocabulary from the task's train set\n", "    decoded_tokens = []\n", "    for token_id in token_ids:\n", "        token = task.train_set.vocabulary.to_string([token_id])\n", "        decoded_tokens.append(token)\n", "    return decoded_tokens\n", "\n", "ostart = 0\n", "count = 20\n", "gid = 15\n", "layer = 0\n", "\n", "# Get the token IDs\n", "token_ids = order[ostart:ostart+count].tolist()\n", "\n", "# Decode the tokens\n", "decoded_tokens = decode_tokens(token_ids)\n", "\n", "# Print token IDs and their decoded values\n", "for i, (token_id, token_text) in enumerate(zip(token_ids, decoded_tokens)):\n", "    print(f\"Token {i}: ID={token_id}, Text='{token_text}'\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([16, 1, 66, 8000])"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["counts_o.size()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["labels = task.train_set.vocabulary(order[ostart:ostart+count].tolist())\n", "save_experts_assign = np.zeros((counts_o.size(0), counts_o.size(2), counts_o.size(3)))\n", "\n", "for layer in range(counts_o.size(0)):\n", "    counts_o_layer = counts_o[layer, 0, :, :]\n", "\n", "    # get top_k\n", "    score, index = counts_o_layer.topk(6, dim=-1, sorted=False)\n", "    score = score / (score.sum(dim=-1, keepdim=True) + 1e-20)\n", "    counts_o_layer_topk = torch.zeros_like(counts_o_layer)\n", "    counts_o_layer_topk = counts_o_layer_topk.scatter(-1, index, score)\n", "\n", "    save_experts_assign[layer] = counts_o_layer_topk.numpy()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["np.save(f\"/cm/shared/thongdt4/moeut_training_code/paper/deepseek/router_saturation/smoe/{sys.argv[-1].split('/')[-1].split('.')[0]}.npy\", save_experts_assign)"]}], "metadata": {"kernelspec": {"display_name": "moeut", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}