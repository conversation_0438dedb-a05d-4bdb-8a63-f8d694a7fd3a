program: main.py
command:
  - ${env}
  - /cm/archive/thongdt4/miniconda3/envs/moeut/bin/python3.10
  - ${program}
  - ${args}
method: grid
metric:
  name: validation/mean_accuracy
  goal: maximize
parameters:
  log:
    value: wandb
  task:
    value: slimpajama_transformer
  test_interval:
    value: 10000
  state_size:
    value: 512
  transformer.encoder_n_layers:
    value: 16
  transformer.n_heads:
    value: 4
  dropout:
    value: 0.0
  moe.drop_expert:
    value: 0.0
  lr:
    value: 0.00025
  optimizer:
    value: adamw
  lm.unroll:
    value: 1024
  grad_clip:
    value: 0.1
  amp:
    value: 1
  save_interval:
    value: 10000
  transformer.variant:
    value: preln_moe
  stop_after:
    value: 100000
  moe.n_experts:
    value: 66
  moe.expert_size:
    value: 128
  pkm.n_heads:
    value: 8
  transformer.p_drop_layer:
    value: 0.0
  moe.selection_mode:
    value: gate
  moe.perplexity_reg_mode:
    value: standard
  moe.perplexity_reg:
    value: 0.01
  lr_sched.type:
    value: cos
  lmds.valid_ratio:
    value: 0.005
  transformer.head_projection_size:
    value: 82
  transformer.universal.group_size:
    value: 16
  wd:
    value: 0.01
  lm.trafo.context_blocks:
    value: 0
  min_lr_multiplier:
    value: 0.1
  details_log_interval:
    value: 500
  lm.eval.enabled:
    value: 0
  batch_size:
    value: 64
  per_device_batch_size:
    value: 16
  n_microbatch:
    value: null
  gpu:
    value: 0,1,2,3