program: main.py
command:
  - ${env}
  - /cm/archive/thongdt4/miniconda3/envs/moeut/bin/python3.10
  - ${program}
  - ${args}
method: grid
metric:
  name: validation/mean_accuracy
  goal: maximize
parameters:
  log:
    value: wandb
  task:
    value: c4_transformer
  test_interval:
    value: 2000
  state_size:
    value: 412
  transformer.ff_multiplier:
    value: 4.985
  transformer.encoder_n_layers:
    value: 16
  transformer.n_heads:
    value: 10
  dropout:
    value: 0.0
  lr:
    value: 0.00025
  optimizer:
    value: adamw
  lm.unroll:
    value: 1024
  batch_size:
    value: 64
  grad_clip:
    value: 0.1
  amp:
    value: 1
  save_interval:
    value: 10000
  transformer.variant:
    value: preln_rope
  stop_after:
    value: 100000
  lr_sched.type:
    value: cos
  transformer.head_projection_size:
    value: 41
  lmds.valid_ratio:
    value: 0.005
  wd:
    value: 0.01
  lm.trafo.context_blocks:
    value: 0
  min_lr_multiplier:
    value: 0.1
  details_log_interval:
    value: 500
  gpu:
    value: 3