#!/usr/bin/env python3
"""
Example usage of the Default MoE implementation.

This example shows how to use the Default MoE layer with the integrated default vector functionality.
"""

import torch
import torch.nn as nn
from layers.moe_layer_default_moe import MoE

def example_default_moe():
    """Example of using Default MoE layer."""

    # Model parameters
    batch_size = 4
    seq_len = 128
    dmodel = 768
    n_experts = 8
    expert_size = 256
    n_heads = 2  # Number of experts to select per token

    # Create Default MoE layer
    default_moe = MoE(
        dmodel=dmodel,
        n_experts=n_experts,
        expert_size=expert_size,
        n_heads=n_heads,
        dropout=0.1,
        weight_scale=1.0,
        selection_mode="gate",  # or "sigmoid"
        perplexity_reg=0.01,
        perplexity_reg_mode="standard",
        bias=True,
        # Default MoE specific parameters
        use_default_vector=True,  # Enable Default MoE functionality
        vector_beta=0.99,  # EMA coefficient for default vector updates
        log_interval=10
    )

    # Create input tensor
    input_tensor = torch.randn(batch_size, seq_len, dmodel)

    print(f"Input shape: {input_tensor.shape}")
    print(f"Number of experts: {n_experts}")
    print(f"Number of selected experts per token: {n_heads}")
    print(f"Default vector enabled: {default_moe.use_default_vector}")
    print(f"EMA coefficient (vector_beta): {default_moe.vector_beta}")

    # Forward pass
    default_moe.train()  # Set to training mode for EMA updates
    output = default_moe(input_tensor)

    print(f"Output shape: {output.shape}")
    print(f"Default vector shape: {default_moe.default_vector.shape}")
    print(f"Default vector norm (per expert): {default_moe.default_vector.norm(dim=-1)}")

    return output, default_moe

def compare_with_without_default_vector():
    """Compare MoE with and without default vector."""

    # Parameters
    batch_size = 2
    seq_len = 64
    dmodel = 512
    n_experts = 4
    expert_size = 128
    n_heads = 2

    # Create input
    input_tensor = torch.randn(batch_size, seq_len, dmodel)

    # Standard MoE (without default vector)
    standard_moe = MoE(
        dmodel=dmodel,
        n_experts=n_experts,
        expert_size=expert_size,
        n_heads=n_heads,
        use_default_vector=False,  # Disable Default MoE
        selection_mode="gate"
    )

    # Default MoE (with default vector)
    default_moe = MoE(
        dmodel=dmodel,
        n_experts=n_experts,
        expert_size=expert_size,
        n_heads=n_heads,
        use_default_vector=True,  # Enable Default MoE
        vector_beta=0.95,
        selection_mode="gate"
    )

    # Forward passes
    standard_moe.eval()
    default_moe.eval()

    with torch.no_grad():
        standard_output = standard_moe(input_tensor)
        default_output = default_moe(input_tensor)

    print("Comparison of Standard MoE vs Default MoE:")
    print(f"Standard MoE output shape: {standard_output.shape}")
    print(f"Default MoE output shape: {default_output.shape}")
    print(f"Output difference norm: {(default_output - standard_output).norm()}")
    print(f"Default vector contribution exists: {hasattr(default_moe, 'default_vector')}")

    return standard_output, default_output

if __name__ == "__main__":
    print("=== Default MoE Example ===")
    output, moe_layer = example_default_moe()

    print("\n=== Comparison Example ===")
    standard_out, default_out = compare_with_without_default_vector()

    print("\n=== Training Example (EMA Updates) ===")
    # Show how default vector changes during training
    moe_layer.train()
    initial_default_vector = moe_layer.default_vector.clone()

    # Multiple forward passes to see EMA updates
    for i in range(5):
        input_batch = torch.randn(2, 32, 768)
        _ = moe_layer(input_batch)
        current_norm = moe_layer.default_vector.norm(dim=-1).mean()
        print(f"Step {i+1}: Default vector mean norm = {current_norm:.4f}")

    final_default_vector = moe_layer.default_vector
    change_norm = (final_default_vector - initial_default_vector).norm()
    print(f"Total change in default vector: {change_norm:.4f}")