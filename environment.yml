name: moeut
channels:
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - asttokens=3.0.0=pyhd8ed1ab_1
  - bzip2=1.0.8=h5eee18b_6
  - ca-certificates=2025.1.31=hbcca054_0
  - comm=0.2.2=pyhd8ed1ab_1
  - debugpy=1.8.12=py310hf71b8c6_0
  - exceptiongroup=1.2.2=pyhd8ed1ab_1
  - executing=2.1.0=pyhd8ed1ab_1
  - importlib-metadata=8.6.1=pyha770c72_0
  - ipykernel=6.29.5=pyh3099207_0
  - ipython=8.33.0=pyh907856f_0
  - jedi=0.19.2=pyhd8ed1ab_1
  - jupyter_client=8.6.3=pyhd8ed1ab_1
  - jupyter_core=5.7.2=pyh31011fe_1
  - krb5=1.21.3=h143b758_0
  - ld_impl_linux-64=2.40=h12ee557_0
  - libedit=3.1.20230828=h5eee18b_0
  - libffi=3.4.4=h6a678d5_1
  - libgcc=14.2.0=h767d61c_2
  - libgcc-ng=14.2.0=h69a702a_2
  - libgomp=14.2.0=h767d61c_2
  - libsodium=1.0.20=h4ab18f5_0
  - libstdcxx=14.2.0=h8f9b012_2
  - libstdcxx-ng=11.2.0=h1234567_1
  - libuuid=1.41.5=h5eee18b_0
  - matplotlib-inline=0.1.7=pyhd8ed1ab_1
  - ncurses=6.4=h6a678d5_0
  - nest-asyncio=1.6.0=pyhd8ed1ab_1
  - openssl=3.4.1=h7b32b05_0
  - packaging=24.2=pyhd8ed1ab_2
  - parso=0.8.4=pyhd8ed1ab_1
  - pexpect=4.9.0=pyhd8ed1ab_1
  - pickleshare=0.7.5=pyhd8ed1ab_1004
  - pip=25.0=py310h06a4308_0
  - platformdirs=4.3.6=pyhd8ed1ab_1
  - prompt-toolkit=3.0.50=pyha770c72_0
  - ptyprocess=0.7.0=pyhd8ed1ab_1
  - pure_eval=0.2.3=pyhd8ed1ab_1
  - pygments=2.19.1=pyhd8ed1ab_0
  - python=3.10.16=he870216_1
  - python-dateutil=2.9.0.post0=pyhff2d567_1
  - python_abi=3.10=2_cp310
  - pyzmq=26.2.1=py310h71f11fc_0
  - readline=8.2=h5eee18b_0
  - setuptools=75.8.0=py310h06a4308_0
  - six=1.17.0=pyhd8ed1ab_0
  - sqlite=3.45.3=h5eee18b_0
  - stack_data=0.6.3=pyhd8ed1ab_1
  - tk=8.6.14=h39e8969_0
  - tornado=6.4.2=py310ha75aee5_0
  - traitlets=5.14.3=pyhd8ed1ab_1
  - typing_extensions=4.12.2=pyha770c72_1
  - wcwidth=0.2.13=pyhd8ed1ab_1
  - wheel=0.45.1=py310h06a4308_0
  - xz=5.4.6=h5eee18b_1
  - zeromq=4.3.5=h3b0a872_7
  - zipp=3.21.0=pyhd8ed1ab_1
  - zlib=1.2.13=h5eee18b_1
  - pip:
      - absl-py==2.1.0
      - accelerate==1.4.0
      - acres==0.5.0
      - aiofiles==24.1.0
      - aiohappyeyeballs==2.5.0
      - aiohttp==3.11.13
      - aiosignal==1.3.2
      - annotated-types==0.7.0
      - anyio==4.9.0
      - async-timeout==5.0.1
      - attrs==25.1.0
      - certifi==2025.1.31
      - chardet==5.2.0
      - charset-normalizer==3.4.1
      - ci-info==0.3.0
      - click==8.1.8
      - colorama==0.4.6
      - configobj==5.0.9
      - configparser==7.2.0
      - contourpy==1.3.1
      - cycler==0.12.1
      - cython==3.0.11
      - dataclasses==0.6
      - dataproperty==1.1.0
      - datasets==3.3.2
      - decorator==5.1.1
      - deprecated==1.2.18
      - dill==0.3.8
      - docker-pycreds==0.4.0
      - etelemetry==0.3.1
      - evaluate==0.4.3
      - filelock==3.13.1
      - fitz==0.0.1.dev2
      - fonttools==4.55.8
      - frontend==0.0.3
      - frozenlist==1.5.0
      - fsspec==2024.6.1
      - ftfy==6.3.1
      - future==1.0.0
      - gitdb==4.0.12
      - gitpython==3.1.44
      - grpcio==1.70.0
      - h11==0.16.0
      - httplib2==0.22.0
      - huggingface-hub==0.28.1
      - idna==3.10
      - imageio==2.37.0
      - imageio-ffmpeg==0.6.0
      - importlib-resources==6.5.2
      - isodate==0.7.2
      - itsdangerous==2.2.0
      - jinja2==3.1.4
      - joblib==1.4.2
      - jsonlines==4.0.0
      - kiwisolver==1.4.8
      - lm-eval==0.4.8
      - looseversion==1.3.0
      - lxml==5.3.1
      - markdown==3.7
      - markupsafe==2.1.5
      - matplotlib==3.10.0
      - mbstrdecoder==1.1.4
      - more-itertools==10.6.0
      - moviepy==2.1.2
      - mpmath==1.3.0
      - multidict==6.1.0
      - multiprocess==0.70.16
      - mypy==1.17.0
      - mypy-extensions==1.1.0
      - narwhals==1.25.1
      - networkx==3.3
      - nibabel==5.3.2
      - ninja==********
      - nipype==1.10.0
      - nltk==3.9.1
      - numexpr==2.10.2
      - numpy==2.1.2
      - nvidia-cublas-cu11==*********
      - nvidia-cuda-cupti-cu11==11.8.87
      - nvidia-cuda-nvrtc-cu11==11.8.89
      - nvidia-cuda-runtime-cu11==11.8.89
      - nvidia-cudnn-cu11==********
      - nvidia-cufft-cu11==*********
      - nvidia-curand-cu11==*********
      - nvidia-cusolver-cu11==*********
      - nvidia-cusparse-cu11==*********
      - nvidia-nccl-cu11==2.20.5
      - nvidia-nvtx-cu11==11.8.86
      - opencv-python==*********
      - pandas==2.2.3
      - pathlib==1.0.1
      - pathspec==0.12.1
      - pathvalidate==3.2.3
      - pdf2image==1.17.0
      - pdfrw==0.4
      - peft==0.14.0
      - pikepdf==9.10.2
      - pillow==10.4.0
      - plotly==6.0.0
      - portalocker==3.1.1
      - primefac==2.0.12
      - proglog==0.1.10
      - propcache==0.3.0
      - protobuf==5.29.3
      - prov==2.1.1
      - psutil==6.1.1
      - puremagic==1.30
      - pyarrow==19.0.1
      - pybind11==2.13.6
      - pydantic==2.10.6
      - pydantic-core==2.27.2
      - pydot==4.0.1
      - pyparsing==3.2.1
      - pytablewriter==1.2.1
      - python-dotenv==1.0.1
      - python-hostlist==2.2.1
      - pytz==2025.1
      - pyxnat==1.6.3
      - pyyaml==6.0.2
      - rdflib==7.1.4
      - regex==2024.11.6
      - reportlab==4.4.3
      - requests==2.32.3
      - rouge-score==0.1.2
      - sacrebleu==2.5.1
      - safetensors==0.5.2
      - scikit-learn==1.6.1
      - scipy==1.15.1
      - seaborn==0.13.2
      - sentencepiece==0.2.0
      - sentry-sdk==2.20.0
      - setproctitle==1.3.4
      - simplejson==3.20.1
      - smmap==5.0.2
      - sniffio==1.3.1
      - sqlitedict==2.1.0
      - starlette==0.47.2
      - sympy==1.13.1
      - tabledata==1.3.4
      - tabulate==0.9.0
      - tcolorpy==0.1.7
      - tensorboard==2.18.0
      - tensorboard-data-server==0.7.2
      - threadpoolctl==3.5.0
      - tokenizers==0.21.0
      - tomli==2.2.1
      - torch==2.3.1+cu118
      - torchaudio==2.3.1+cu118
      - torchvision==0.18.1+cu118
      - tqdm==4.67.1
      - tqdm-multiprocess==0.0.11
      - traits==7.0.2
      - transformers==4.48.2
      - triton==2.3.1
      - typepy==1.3.4
      - tzdata==2025.1
      - urllib3==2.3.0
      - uvicorn==0.35.0
      - wandb==0.19.6
      - werkzeug==3.1.3
      - word2number==1.1
      - wrapt==1.17.2
      - xxhash==3.5.0
      - yarl==1.18.3
      - zstandard==0.23.0
prefix: /root/miniconda3/envs/moeut
