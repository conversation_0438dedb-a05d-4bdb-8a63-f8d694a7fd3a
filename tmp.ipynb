{"cells": [{"cell_type": "code", "execution_count": 1, "id": "ac6dd082", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["hello\n"]}], "source": ["print(\"hello\")"]}, {"cell_type": "code", "execution_count": 5, "id": "539a6180", "metadata": {}, "outputs": [], "source": ["import collections\n", "\n", "class AliasedIngredients(collections.abc.Set):\n", "    def __init__(self, ingredients: set[str]):\n", "        self.ingredients = ingredients\n", "        \n", "    def __contains__(self, value: str):\n", "        return value in self.ingredients or any(alias in self.ingredients \n", "                                                for alias in get_aliases(value))\n", "    \n", "    def __iter__(self):\n", "        return iter(self.ingredients)\n", "    \n", "    def __len__(self):\n", "        return len(self.ingredients)"]}, {"cell_type": "code", "execution_count": 6, "id": "23a6d9df", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["salt\n", "sugar\n", "pepper\n"]}], "source": ["ingredients = AliasedIngredients({\"salt\", \"pepper\", \"sugar\"})\n", "for i in ingredients:\n", "    print(i)"]}], "metadata": {"kernelspec": {"display_name": "moeut", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}