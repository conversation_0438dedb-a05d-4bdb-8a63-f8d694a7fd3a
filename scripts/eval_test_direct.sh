#!/bin/bash

echo "Direct evaluation test..."

# Activate conda environment
source /cm/archive/thongdt4/miniconda3/etc/profile.d/conda.sh
conda activate moeut

# GPU setup - single GPU to avoid distributed issues
export MOE_TYPE="moe_layer"
export CUDA_VISIBLE_DEVICES="0"
export PYTHONPATH="/cm/archive/thongdt4/moeut_training_code:$PYTHONPATH"

# Test parameters
checkpoint_path="/cm/archive/thongdt4/moeut_training_code/save_final/slimpajama_moe_no_attmoe_660M_standardlb/checkpoint/model-400000.pth"
save_dir="/cm/archive/thongdt4/moeut_training_code/save_final/slimpajama_moe_no_attmoe_660M_standardlb/tmp"
bs=16

# Create save directory if it doesn't exist
mkdir -p "$save_dir"

echo "Running direct evaluation..."
echo "Checkpoint: $checkpoint_path"
echo "Save dir: $save_dir"
echo "Batch size: $bs"

# Run the evaluation directly
timeout 3600 python3 main.py \
    --name validate3 \
    --log tb \
    --restore "$checkpoint_path" \
    --test_only 1 \
    -reset 1 \
    -lm.eval.enabled 1 \
    -lm.eval.sciq.enabled 1 \
    --keep_alive 0 \
    --batch_size "$bs"

exit_code=$?

if [ $exit_code -eq 124 ]; then
    echo "ERROR: Process timed out after 1 hour"
elif [ $exit_code -ne 0 ]; then
    echo "ERROR: Process failed with exit code $exit_code"
else
    echo "Evaluation completed successfully!"
fi

echo "Exit code: $exit_code"
