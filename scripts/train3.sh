echo "Training ..."

export MOE_TYPE="moe_layer_deepseek_sigmoidonly_4share"
export CUDA_VISIBLE_DEVICES="4,5,6,7"
export MASTER_PORT=29566
export CUDA_LAUNCH_BLOCKING=1
export NCCL_DEBUG=INFO
export TORCH_DISTRIBUTED_DEBUG=INFO
NUM_DEVICES=$(echo $CUDA_VISIBLE_DEVICES | tr ',' '\n' | wc -l)


# torchrun --master_port $MASTER_PORT --nproc_per_node=$NUM_DEVICES run.py  \
#     /cm/archive/thongdt4/moeut_training_code/sweeps/experiments/proposed_method/154M/slimpajama_moe_no_attmqqoe_154M_standard_lb_remoe.yaml


deepspeed --master_port $MASTER_PORT --include localhost:$CUDA_VISIBLE_DEVICES run_deepspeed.py \
    /cm/archive/thongdt4/moeut_training_code/sweeps/experiments/proposed_method/rebuttal/slimpajama_moe_no_attmoe_154M_deepseek_sigmoidonly_4share.yaml


# bash /cm/shared/thongdt4/moeut_training_code/scripts/train4.sh
