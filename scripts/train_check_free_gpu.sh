#!/usr/bin/env bash
set -euo pipefail

# GPUs to check (comma-separated)
export CUDA_VISIBLE_DEVICES="0,1,2,3"

# --- Configuration ---
THRESHOLD=99                       # minimum free VRAM % required
# split into array properly
IFS=',' read -ra GPUS <<< "$CUDA_VISIBLE_DEVICES"
NUM_DEVICES=${#GPUS[@]}            # number of GPUs

echo "Waiting for all GPUs to have ≥${THRESHOLD}% free VRAM..."
echo "Will check GPUs: ${GPUS[*]} every 10 seconds"
echo

# Continuous checking loop
while true; do
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Checking VRAM on GPUs: ${GPUS[*]}"

    all_gpus_ready=true

    for GPU in "${GPUS[@]}"; do
        # query total and used (MiB), strip commas, then split on whitespace
        stats=$(nvidia-smi \
          --query-gpu=memory.total,memory.used \
          --format=csv,noheader,nounits -i "$GPU" \
          | tr -d ',')
        read -r total used <<< "$stats"

        free=$(( total - used ))
        pct_free=$(( free * 100 / total ))

        if (( pct_free < THRESHOLD )); then
            echo "  ✖ GPU $GPU: only ${pct_free}% free (< ${THRESHOLD}%)"
            all_gpus_ready=false
        else
            echo "  ✔ GPU $GPU: ${pct_free}% free"
        fi
    done

    if [ "$all_gpus_ready" = true ]; then
        echo
        echo "All $NUM_DEVICES GPUs have ≥${THRESHOLD}% free VRAM! Starting training…"
        echo
        break
    else
        echo "  Some GPUs don't meet requirements. Sleeping 10 seconds..."
        echo
        sleep 10
    fi
done

# --- Environment Setup ---
export MOE_TYPE="moe_layer_deepseek_sigmoidonly_4share"
export CUDA_VISIBLE_DEVICES="0,1,2,3"
export MASTER_PORT=29566
export CUDA_LAUNCH_BLOCKING=1
export NCCL_DEBUG=INFO
export TORCH_DISTRIBUTED_DEBUG=INFO
NUM_DEVICES=$(echo $CUDA_VISIBLE_DEVICES | tr ',' '\n' | wc -l)


# torchrun --master_port $MASTER_PORT --nproc_per_node=$NUM_DEVICES run.py  \
#     /cm/archive/thongdt4/moeut_training_code/sweeps/experiments/proposed_method/154M/slimpajama_moe_no_attmqqoe_154M_standard_lb_remoe.yaml


deepspeed --master_port $MASTER_PORT --include localhost:$CUDA_VISIBLE_DEVICES run_deepspeed.py \
    /cm/archive/thongdt4/moeut_training_code/sweeps/experiments/proposed_method/rebuttal/slimpajama_moe_no_attmoe_154M_deepseek_sigmoidonly_4share.yaml
