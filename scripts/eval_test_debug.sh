#!/bin/bash
set -e  # Exit on any error
set -x  # Print all commands

echo "Starting evaluation with debugging..."

# GPU setup
export MOE_TYPE="moe_layer_tcmoe"
export CUDA_VISIBLE_DEVICES="0,7"
export PYTHONPATH="/cm/archive/thongdt4/moeut_training_code:$PYTHONPATH"
export MASTER_PORT=29551

# Disable DeepSpeed autotune to prevent hanging
export DS_SKIP_AUTOTUNE=1
# Disable Triton autotune to prevent hanging
export TRITON_DISABLE_AUTOTUNE=1
# Set timeout for distributed operations
export NCCL_TIMEOUT=1800
export TORCH_DISTRIBUTED_TIMEOUT=1800
# Disable CUDA cache to prevent hanging
export CUDA_CACHE_DISABLE=1

NUM_DEVICES=$(echo $CUDA_VISIBLE_DEVICES | tr ',' '\n' | wc -l)
echo "Number of devices: $NUM_DEVICES"

# prepare tests
tasks=(
    # all tasks in the framework/dataset/text
    # "lambada"
    # "cbt"
    # "hellaswag"
    "piqa"
    # "blimp"
    # "ai2arc"
    # "siqa"
    # "commonsenseqa"
    # "race"

    # additional tasks
    # "mmlu"
    # "openbookqa"
    # "winogrande"
)

# run tests
checkpoint_path="/cm/archive/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_standard_lb_tcmoe/checkpoint"
save_dir="/cm/archive/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_standard_lb_tcmoe/export"
bs=16

# Create save directory if it doesn't exist
mkdir -p "$save_dir"

# Initialize tasks_script as empty
tasks_script=""
for task in "${tasks[@]}"; do
    tasks_script="${tasks_script} -lm.eval.${task}.enabled 1"
done

echo "Tasks script: $tasks_script"
echo "Checkpoint path: $checkpoint_path"
echo "Save directory: $save_dir"
echo "Environment variables:"
env | grep -E "(DS_|TRITON_|NCCL_|TORCH_|CUDA_)" | sort

# Function to handle cleanup on exit
cleanup() {
    echo "Cleaning up processes..."
    pkill -f deepspeed || true
    pkill -f python3 || true
    exit 1
}

# Set trap for cleanup
trap cleanup SIGINT SIGTERM

# check if checkpoint_path is a file or directory
if [ -f "$checkpoint_path" ]; then
    echo "Checkpoint path is a file"
    echo "Running single evaluation..."

    timeout 7200 deepspeed --master_port $MASTER_PORT --include localhost:$CUDA_VISIBLE_DEVICES paper/moe_universal/run_tests_deepspeed.py \
        --tasks "$tasks_script" \
        --path_weight "$checkpoint_path" \
        --save_dir "$save_dir" \
        --bs "$bs" || {
            echo "ERROR: Evaluation timed out or failed"
            cleanup
        }

elif [ -d "$checkpoint_path" ]; then
    echo "Checkpoint path is a directory"
    echo "Finding checkpoint files..."

    file_list=$(find "$checkpoint_path" -type f -name "*.pth" | head -5)  # Limit to 5 files for testing

    if [ -z "$file_list" ]; then
        echo "ERROR: No .pth files found in $checkpoint_path"
        exit 1
    fi

    echo "Found checkpoint files:"
    echo "$file_list"

    for file_path in $file_list; do
        echo "Processing file: $file_path"
        echo "Starting evaluation at $(date)"

        timeout 7200 deepspeed --master_port $MASTER_PORT --include localhost:$CUDA_VISIBLE_DEVICES paper/moe_universal/run_tests_deepspeed.py \
            --tasks "$tasks_script" \
            --path_weight "$file_path" \
            --save_dir "$save_dir" \
            --bs "$bs" || {
                echo "ERROR: Evaluation failed for $file_path"
                continue
            }

        echo "Completed evaluation for $file_path at $(date)"
    done
else
    echo "ERROR: Checkpoint path $checkpoint_path does not exist or is not accessible"
    exit 1
fi

echo "Evaluation completed successfully at $(date)"