echo "Training ..."

export MOE_TYPE="moe_layer"
export CUDA_VISIBLE_DEVICES="4"
export MASTER_PORT=29590
export CUDA_LAUNCH_BLOCKING=1
export NCCL_DEBUG=INFO
export TORCH_DISTRIBUTED_DEBUG=INFO

NUM_DEVICES=$(echo $CUDA_VISIBLE_DEVICES | tr ',' '\n' | wc -l)


torchrun --master_port $MASTER_PORT --nproc_per_node=$NUM_DEVICES run.py  \
    /cm/archive/thongdt4/moeut_training_code/sweeps/experiments/proposed_method/test.yaml

# deepspeed --master_port $MASTER_PORT --include localhost:$CUDA_VISIBLE_DEVICES run_deepspeed.py \
#     /cm/archive/thongdt4/moeut_training_code/sweeps/experiments/proposed_method/test.yaml

# bash /cm/shared/thongdt4/moeut_training_code/scripts/train4.sh
# source /cm/archive/thongdt4/miniconda3/bin/activate moeut