echo "Evaluation ... "

# GPU setup - Try single GPU first to avoid distributed issues
export MOE_TYPE="moe_layer"
export CUDA_VISIBLE_DEVICES="0,1,2,3"
export PYTHONPATH="/cm/archive/thongdt4/moeut_training_code:$PYTHONPATH"
export MASTER_PORT=29607
export NCCL_TIMEOUT=3600
export NCCL_DEBUG=INFO
export NCCL_IB_TIMEOUT=23
export NCCL_SOCKET_TIMEOUT=3600

# Add PyTorch timeout settings
export TORCH_DISTRIBUTED_TIMEOUT=3600
NUM_DEVICES=$(echo $CUDA_VISIBLE_DEVICES | tr ',' '\n' | wc -l)

# Function to handle cleanup on exit
cleanup() {
    echo "Cleaning up processes..."
    pkill -f torchrun || true
    pkill -f python3 || true
    exit 1
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# prepare tests
tasks=(
    # all tasks in the framework/dataset/text
    "lambada"
    "cbt"
    "hellaswag"
    "piqa"
    "blimp"
    "ai2arc"
    "siqa"
    "commonsenseqa"
    "race"

    # "sciq"
    # additional tasks
    # "boolq"
    # "mmlu"
    # "openbookqa"
    # "winogrande"
)

# run tests
checkpoint_path="/cm/archive/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_sigmoid_standard_lb_revise/checkpoint/model-100000.pth"
save_dir="/cm/archive/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_sigmoid_standard_lb_revise/export"
bs=16

for task in "${tasks[@]}"; do
    # tasks_script="${tasks_script} -lm.eval.${task}.enabled 1"
    tasks_script="${tasks_script} -lm.eval.${task}.enabled 1"
done

echo $tasks_script

# Activate conda environment properly
source /cm/archive/thongdt4/miniconda3/etc/profile.d/conda.sh
conda activate moeut

# check if checkpoint_path is a file or directory
if [ -f "$checkpoint_path" ]; then
    echo "Checkpoint path is a file"
    # run tests single with timeout - use direct Python call instead of torchrun to avoid distributed issues
    timeout 7200 python3 paper/moe_universal/run_tests.py \
        --tasks "$tasks_script" \
        --path_weight "$checkpoint_path" \
        --save_dir "$save_dir" \
        --bs "$bs"

    exit_code=$?
    if [ $exit_code -eq 124 ]; then
        echo "ERROR: Process timed out after 2 hours"
        cleanup
    elif [ $exit_code -ne 0 ]; then
        echo "ERROR: Process failed with exit code $exit_code"
        cleanup
    fi
else
    echo "Checkpoint path is a directory"
    # run tests all
    file_list=$(find "$checkpoint_path" -type f)
    for file_path in $file_list; do
        echo "Processing file: $file_path"
        timeout 7200 python3 paper/moe_universal/run_tests.py \
            --tasks "$tasks_script" \
            --path_weight "$file_path" \
            --save_dir "$save_dir" \
            --bs "$bs"

        exit_code=$?
        if [ $exit_code -eq 124 ]; then
            echo "ERROR: Process timed out after 2 hours for file: $file_path"
            continue
        elif [ $exit_code -ne 0 ]; then
            echo "ERROR: Process failed with exit code $exit_code for file: $file_path"
            continue
        fi
    done
fi

echo "Evaluation completed successfully!"

