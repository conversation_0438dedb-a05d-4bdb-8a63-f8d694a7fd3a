echo "Evaluation ... "

# GPU setup
export MOE_TYPE="moe_layer_tcmoe"
export CUDA_VISIBLE_DEVICES="7,6"
export PYTHONPATH="/cm/archive/thongdt4/moeut_training_code:$PYTHONPATH"
export MASTER_PORT=29551
# Disable DeepSpeed autotune to prevent hanging
export DS_SKIP_AUTOTUNE=1
# Disable Triton autotune to prevent hanging
export TRITON_DISABLE_AUTOTUNE=1
# Set timeout for distributed operations
export NCCL_TIMEOUT=1800
export TORCH_DISTRIBUTED_TIMEOUT=1800
# Disable CUDA cache to prevent hanging
export CUDA_CACHE_DISABLE=1
NUM_DEVICES=$(echo $CUDA_VISIBLE_DEVICES | tr ',' '\n' | wc -l)

# prepare tests
tasks=(
    # all tasks in the framework/dataset/text
    # "lambada"
    # "cbt"
    # "hellaswag"
    "piqa"
    # "blimp"
    # "ai2arc"
    # "siqa"
    # "commonsenseqa"
    # "race"

    # additional tasks
    # "mmlu"
    # "openbookqa"
    # "winogrande"
)


# run tests
checkpoint_path="/cm/archive/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_standard_lb_tcmoe/checkpoint"
save_dir="/cm/archive/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_standard_lb_tcmoe/export"
bs=16

# Initialize tasks_script as empty
tasks_script=""
for task in "${tasks[@]}"; do
    tasks_script="${tasks_script} -lm.eval.${task}.enabled 1"
done

echo "Tasks script: $tasks_script"
echo "Checkpoint path: $checkpoint_path"
echo "Save directory: $save_dir"

# check if checkpoint_path is a file or directory
if [ -f "$checkpoint_path" ]; then
    echo "Checkpoint path is a file"
    # run tests single
    # torchrun --master_port $MASTER_PORT --nproc_per_node=$NUM_DEVICES paper/moe_universal/run_tests.py \
    #     --tasks "$tasks_script" \
    #     --path_weight "$checkpoint_path" \
    #     --save_dir "$save_dir" \
    #     --bs "$bs"
    deepspeed --master_port $MASTER_PORT --include localhost:$CUDA_VISIBLE_DEVICES paper/moe_universal/run_tests_deepspeed.py \
        --tasks "$tasks_script" \
        --path_weight "$checkpoint_path" \
        --save_dir "$save_dir" \
        --bs "$bs"
else
    echo "Checkpoint path is a directory"
    # run tests all
    file_list=$(find "$checkpoint_path" -type f)
    for file_path in $file_list; do
        echo "Processing file: $file_path"
        # torchrun --master_port $MASTER_PORT --nproc_per_node=$NUM_DEVICES paper/moe_universal/run_tests.py \
        #     --tasks "$tasks_script" \
        #     --path_weight "$file_path" \
        #     --save_dir "$save_dir" \
        #     --bs "$bs"
        deepspeed --master_port $MASTER_PORT --include localhost:$CUDA_VISIBLE_DEVICES paper/moe_universal/run_tests_deepspeed.py \
            --tasks "$tasks_script" \
            --path_weight "$file_path" \
            --save_dir "$save_dir" \
            --bs "$bs"
    done
fi

echo "Evaluation done ..."

# bash /cm/shared/thongdt4/moeut_training_code/scripts/eval_test2.sh